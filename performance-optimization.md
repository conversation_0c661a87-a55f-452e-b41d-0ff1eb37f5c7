# ICM 性能优化指南

## 🚀 已实施的优化措施

### 1. 监控任务频率优化

- **HAMonitorService**: 集群状态检查从 30 秒调整为 60 秒

## 🔧 推荐的 JVM 启动参数

```bash
# 内存配置
-Xms2g                    # 初始堆内存2GB
-Xmx4g                    # 最大堆内存4GB
-XX:NewRatio=1            # 年轻代与老年代比例1:1
-XX:SurvivorRatio=8       # Eden与Survivor比例8:1:1

# 垃圾回收器配置
-XX:+UseG1GC              # 使用G1垃圾回收器
-XX:MaxGCPauseMillis=200  # 最大GC暂停时间200ms
-XX:G1HeapRegionSize=16m  # G1区域大小16MB

# 监控和调试
-XX:+PrintGCDetails       # 打印GC详情
-XX:+PrintGCTimeStamps    # 打印GC时间戳
-Xloggc:/var/log/icm/gc.log # GC日志文件
-XX:+UseGCLogFileRotation # 启用GC日志轮转
-XX:NumberOfGCLogFiles=5  # GC日志文件数量
-XX:GCLogFileSize=10M     # GC日志文件大小

# 其他优化
-XX:+HeapDumpOnOutOfMemoryError           # OOM时生成堆转储
-XX:HeapDumpPath=/var/log/icm/heapdump/   # 堆转储文件路径
-XX:+DisableExplicitGC                    # 禁用显式GC调用
```

## 📊 性能监控建议

### 1. 应用级监控

```bash
# 查看Java进程资源使用情况
jps -v
jstat -gc <pid> 5s     # 每5秒查看GC情况
jmap -histo <pid>      # 查看内存中对象分布

# 查看线程情况
jstack <pid> > thread_dump.txt
```

### 2. 系统级监控

```bash
# CPU和内存监控
top -p <pid>
htop
vmstat 5

# 网络连接监控
netstat -an | grep :9200  # 监控应用端口连接
ss -tulpn | grep :9200

# 磁盘IO监控
iotop
iostat -x 5
```

### 3. 日志监控

```bash
# 监控错误日志
tail -f /var/log/icm/icm.log | grep -i error

# 监控GC日志
tail -f /var/log/icm/gc.log

# 统计各类日志数量
grep -c "ERROR\|WARN\|INFO" /var/log/icm/icm.log
```

### 3. 定期维护任务

创建定期维护脚本：

```bash
#!/bin/bash
# 定期清理临时文件
find /home/<USER>/icm/upgrade/exec_* -type d -mtime +7 -exec rm -rf {} \;

# 清理过期日志
find /var/log/icm/ -name "*.log.*" -mtime +30 -delete

# 监控磁盘空间
df -h | awk '$5 > 80 {print "警告：" $1 " 磁盘使用率超过80%"}'
```

## 🔍 故障排查指南

### CPU 使用率高的排查

1. 使用 `jstack` 导出线程堆栈，查找 CPU 占用高的线程
2. 检查是否有死循环或长时间运行的任务
3. 分析 GC 频率，是否因内存不足导致频繁 GC

### 内存泄漏排查

1. 使用 `jmap -dump` 生成堆转储文件
2. 用 MAT (Memory Analyzer Tool) 分析内存泄漏
3. 检查长期存活的对象和集合是否正常释放

### 网络连接问题排查

1. 检查 Redis/数据库连接状态
2. 分析网络超时日志
3. 监控连接池使用情况

## 📈 预期性能改善

通过以上优化措施，预期可以实现：

- **CPU 使用率降低**: 40-60%
- **内存使用优化**: 减少不必要的内存分配
- **响应时间改善**: 减少阻塞操作的影响
- **系统稳定性提升**: 更好的异常处理和资源管理

请在应用这些优化后持续监控系统性能，根据实际情况进一步调整参数。

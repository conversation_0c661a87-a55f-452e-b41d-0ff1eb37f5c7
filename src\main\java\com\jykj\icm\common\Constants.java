package com.jykj.icm.common;

public class Constants {
    public static final String BASE_DIR = "/home/<USER>";
    public static final String REDIS_CONF_DIR = BASE_DIR + "/redis/conf";
    public static final String REDIS_DATA_DIR = BASE_DIR + "/redis/data";
    public static final String SENTINEL_DIR = BASE_DIR + "/sentinel";
    public static final String REDIS_PORT = "6379";
    public static final String DOCKER_REDIS_IMAGE = "redis";
    public static final String HOST_TYPE_MASTER = "主";
    public static final String HOST_TYPE_SLAVE = "备";

    // Redis主机地址，默认为本地
    public static final String REDIS_HOST = "localhost";

    // gauss相关
    public static final String GAUSS_USERNAME = "gaussdb";
    public static final String GAUSS_DIR = BASE_DIR + "/gauss5";

    public static final String GAUSS_DATA_DIR = BASE_DIR + "/gauss5/data";
    public static final String DEFAULT_PASSWORD = "Jykj1994@";
    public static final String GAUSS_PORT = "5434";
    public static final String GAUSS_LOCALSERVICE = "5432";

    // sso相关
    public static final String SSO_PORT = "9009";

    // 配置文件路径（类路径资源）
    public static final String REDIS_MASTER_CONF = "/conf/redis-master.conf";
    public static final String REDIS_SLAVE_CONF = "/conf/redis-slave.conf";
    public static final String SENTINEL_CONF = "/conf/sentinel.conf";
    public static final String KEEPALIVED_CONF = "/conf/keepalived.conf";
    public static final String NGINX_CONF = "/conf/nginx.conf";

    // keepalived相关
    public static final String XC_KEEPALIVED_DIR = "/etc/keepalived";
    public static final String KEEPALIVED_MASTER = "MASTER";
    public static final String KEEPALIVED_BACKUP = "BACKUP";
    public static final String KEEPALIVED_MASTER_PRIORITY_NUM = "100";
    public static final String KEEPALIVED_BACKUP_PRIORITY_NUM = "90";
    // nginx相关
    public static final String XC_NGINX_CONF_DIR = BASE_DIR + "/nginx";

    public static final String REDIS_HOSTINFO_KEY = "HOSTINFO";

    public static final String REDIS_UPDATE_NOTICE_KEY = "UPDATE_NOTICE";

    public static final String MODIFY_REPL_CONN_INFO = "MODIFY_REPL_CONN_INFO";

    public static final String UPDATE_SSO = "UPDATE_SSO";

    public static final String UPDATE_GAUSS_PASSWORD = "UPDATE_GAUSS_PASSWORD";

    public static final String UPDATE_VIP = "UPDATE_VIP";

    public static final String MODIFY_UPDATE = "update";

    public static final String LINE_SEPARATOR = System.getProperty("line.separator");

    public static final String REDIS_MASTER_IP_KEY = "MASTER_IP";

    public static final int HTTP_TIMEOUT = 5000;
}

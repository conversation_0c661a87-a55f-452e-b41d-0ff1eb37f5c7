package com.jykj.icm.common.result;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 统一返回结果类
 */
@Accessors(chain = true)
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态值：0 为成功，其他数值代表失败
     */
    private Integer status;

    /**
     * 返回信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 成功返回结果
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    /**
     * 成功返回结果
     *
     * @param data 获取的数据
     */
    public static <T> Result<T> success(T data) {
        return success(ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功返回结果
     *
     * @param msg 返回信息
     * @param data 获取的数据
     */
    public static <T> Result<T> success(String msg, T data) {
        return new Result<T>().setStatus(ResultCode.SUCCESS.getCode()).setMsg(msg).setData(data);
    }

    /**
     * 失败返回结果
     */
    public static <T> Result<T> failed() {
        return failed(ResultCode.FAILED);
    }

    /**
     * 失败返回结果
     *
     * @param errorCode 错误码
     */
    public static <T> Result<T> failed(ResultCode errorCode) {
        return new Result<T>().setStatus(errorCode.getCode()).setMsg(errorCode.getMessage());
    }

    /**
     * 失败返回结果
     *
     * @param msg 提示信息
     */
    public static <T> Result<T> failed(String msg) {
        return new Result<T>().setStatus(ResultCode.FAILED.getCode()).setMsg(msg);
    }

    /**
     * 失败返回结果
     *
     * @param code 状态码
     * @param msg 提示信息
     */
    public static <T> Result<T> failed(Integer code, String msg) {
        return new Result<T>().setStatus(code).setMsg(msg);
    }

    /**
     * 参数验证失败返回结果
     */
    public static <T> Result<T> validateFailed() {
        return failed(ResultCode.VALIDATE_FAILED);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param msg 提示信息
     */
    public static <T> Result<T> validateFailed(String msg) {
        return new Result<T>().setStatus(ResultCode.VALIDATE_FAILED.getCode()).setMsg(msg);
    }

    /**
     * 未登录返回结果
     */
    public static <T> Result<T> unauthorized() {
        return failed(ResultCode.UNAUTHORIZED);
    }

    /**
     * 未授权返回结果
     */
    public static <T> Result<T> forbidden() {
        return failed(ResultCode.FORBIDDEN);
    }
}
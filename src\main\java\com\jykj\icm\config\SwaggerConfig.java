package com.jykj.icm.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.ResponseBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Response;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * Swagger配置类
 */
@Configuration
@EnableSwagger2WebMvc
public class SwaggerConfig {

    @Bean
    public Docket createRestApi() {
        // 创建API文档基本信息
        return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo()).select()
            // 指定扫描包含API定义的包
            .apis(RequestHandlerSelectors.basePackage("com.jykj.icm.controller")).paths(PathSelectors.any()).build()
            .useDefaultResponseMessages(false).globalResponses(HttpMethod.GET, getGlobalResponseMessage())
            .globalResponses(HttpMethod.POST, getGlobalResponseMessage())
            .globalResponses(HttpMethod.PUT, getGlobalResponseMessage())
            .globalResponses(HttpMethod.DELETE, getGlobalResponseMessage());
    }

    /**
     * API基本信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder().title("ICM系统API文档").description("初始化、配置、监控系统接口文档")
            .contact(new Contact("JYKJ", "http://www.jykj.com", "<EMAIL>")).version("1.0.0").build();
    }

    /**
     * 全局响应状态码
     */
    private List<Response> getGlobalResponseMessage() {
        List<Response> responseList = new ArrayList<>();
        responseList.add(new ResponseBuilder().code("200").description("操作成功").build());
        responseList.add(new ResponseBuilder().code("400").description("请求参数错误").build());
        responseList.add(new ResponseBuilder().code("401").description("未授权").build());
        responseList.add(new ResponseBuilder().code("403").description("禁止访问").build());
        responseList.add(new ResponseBuilder().code("404").description("资源不存在").build());
        responseList.add(new ResponseBuilder().code("500").description("服务器内部错误").build());
        return responseList;
    }
}
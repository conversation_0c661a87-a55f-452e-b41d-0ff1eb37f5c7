package com.jykj.icm.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.jykj.icm.interceptor.SuperAdminInterceptor;
import com.jykj.icm.interceptor.TokenInterceptor;

/**
 * Web MVC配置 用于注册拦截器和其他Web相关配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private TokenInterceptor tokenInterceptor;

    @Autowired
    private SuperAdminInterceptor superAdminInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Token拦截器，并设置拦截路径和排除路径
        registry.addInterceptor(tokenInterceptor).addPathPatterns("/api/**") // 拦截所有API请求
            .excludePathPatterns("/api/auth/login", // 排除登录接口
                "/swagger-resources/**", // 排除Swagger相关路径
                "/swagger-ui/**", "/v2/api-docs", "/doc.html", "/api/public/**");

        // 注册超级管理员权限校验拦截器
        registry.addInterceptor(superAdminInterceptor).addPathPatterns("/api/**");
    }
}
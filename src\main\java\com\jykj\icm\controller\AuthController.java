package com.jykj.icm.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.ChangePasswordRequest;
import com.jykj.icm.entity.LoginRequest;
import com.jykj.icm.entity.User;
import com.jykj.icm.service.UserService;
import com.jykj.icm.utils.NetworkUtils;
import com.jykj.icm.utils.TokenManager;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "认证管理")
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private TokenManager tokenManager;

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@ApiParam("登录请求参数") @RequestBody LoginRequest request,
        HttpServletRequest httpRequest) {
        User user = userService.login(request);
        if (user != null) {
            // 生成token并存储
            String token = tokenManager.generateToken(user);

            Map<String, Object> response = new HashMap<>();
            response.put("username", user.getUsername());
            response.put("role", user.getRole());
            response.put("needChangePassword", user.isNeedChangePassword());
            response.put("token", token);
            response.put("loginIp", NetworkUtils.getIpAddr(httpRequest));
            return Result.success(response);
        }
        return Result.failed("用户名或密码错误");
    }

    @ApiOperation("修改密码")
    @PostMapping("/change-password")
    public Result<String> changePassword(@ApiParam("修改密码请求参数") @Valid @RequestBody ChangePasswordRequest request,
        HttpServletRequest httpRequest) {
        // 从请求属性中获取用户信息，这是由TokenInterceptor设置的
        User user = (User)httpRequest.getAttribute("user");
        if (user == null) {
            return Result.unauthorized();
        }

        userService.changePassword(user.getUsername(), request);
        user.setNeedChangePassword(false);

        return Result.success("密码修改成功");
    }

    @ApiOperation("跳过修改密码")
    @PostMapping("/skipChangePassword")
    public Result<String> skipChangePassword(HttpServletRequest httpRequest) {
        // 从请求属性中获取用户信息，这是由TokenInterceptor设置的
        User user = (User)httpRequest.getAttribute("user");
        if (user == null) {
            return Result.unauthorized();
        }

        userService.changePassword(user.getUsername(), null);
        user.setNeedChangePassword(false);

        return Result.success("跳过修改密码成功");
    }

    @ApiOperation("退出登录")
    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        // 获取Authorization头中的token
        String token = request.getHeader("Authorization");

        // 如果token有效，则删除它
        if (token != null && tokenManager.validateToken(token)) {
            tokenManager.removeToken(token);
        }

        return Result.success("退出登录成功");
    }

    @ApiOperation("检查登录状态")
    @GetMapping("/check")
    public Result<Map<String, Object>> checkLoginStatus(HttpServletRequest request) {
        // 从请求属性中获取用户信息，这是由TokenInterceptor设置的
        User user = (User)request.getAttribute("user");
        if (user != null) {
            Map<String, Object> response = new HashMap<>();
            response.put("username", user.getUsername());
            response.put("role", user.getRole());
            response.put("needChangePassword", user.isNeedChangePassword());
            return Result.success(response);
        }
        return Result.unauthorized();
    }
}
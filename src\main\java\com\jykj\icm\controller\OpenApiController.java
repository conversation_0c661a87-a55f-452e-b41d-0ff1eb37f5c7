package com.jykj.icm.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jykj.icm.common.exception.BusinessException;
import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.ServiceRequest;
import com.jykj.icm.service.HostConfigServer;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/common")
@RequiredArgsConstructor
@Api(tags = "对外接口")
public class OpenApiController {
    private final HostConfigServer hostConfigServer;

    @Value("${secretKey}")
    private String secretKey;

    @PostMapping("/stopService")
    @ApiOperation("停止服务")
    public Result<String> stopService(@RequestBody ServiceRequest serviceRequest) {
        checkSecret(serviceRequest);
        hostConfigServer.stopService(serviceRequest);
        return Result.success("停止服务成功");
    }

    @PostMapping("/startService")
    @ApiOperation("启动服务")
    public Result<String> startService(@RequestBody ServiceRequest serviceRequest) {
        checkSecret(serviceRequest);
        hostConfigServer.startService(serviceRequest);
        return Result.success("启动服务成功");
    }

    private void checkSecret(ServiceRequest serviceRequest) {
        if (!secretKey.equals(serviceRequest.getSecretKey())) {
            throw new BusinessException("密钥错误");
        }
    }
}

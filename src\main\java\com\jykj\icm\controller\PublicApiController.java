package com.jykj.icm.controller;

import java.io.BufferedReader;
import java.io.InputStreamReader;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jykj.icm.common.result.Result;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 公共API控制器 提供各种免认证的公共接口
 */
@Slf4j
@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@Api(tags = "公共接口服务")
public class PublicApiController {

    /**
     * 执行时间同步脚本 此接口不需要认证，可以被外部系统直接调用
     * 
     * @return 同步结果
     */
    @PostMapping("/syncTime")
    @ApiOperation("执行时间同步脚本")
    public Result<String> syncTime() {
        try {
            log.info("开始执行时间同步脚本");

            // 执行时间同步脚本
            Process process =
                new ProcessBuilder("bash", "/home/<USER>/icm/SetTimeServer.sh").redirectErrorStream(true).start();

            // 读取脚本执行的输出结果
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待脚本执行完成并获取退出码
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("时间同步脚本执行成功: {}", output);
                return Result.success("时间同步成功");
            } else {
                log.error("时间同步脚本执行失败，退出码: {}, 输出: {}", exitCode, output);
                return Result.failed("时间同步失败，请检查服务器日志");
            }
        } catch (Exception e) {
            log.error("执行时间同步脚本失败", e);
            return Result.failed("执行时间同步脚本失败: " + e.getMessage());
        }
    }

    // 这里可以添加更多的公共接口
}
package com.jykj.icm.controller;

import java.io.File;
import java.util.List;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.UpgradeLog;
import com.jykj.icm.entity.UpgradePackage;
import com.jykj.icm.entity.UpgradeRequest;
import com.jykj.icm.entity.UpgradeTask;
import com.jykj.icm.service.UpgradeService;
import com.jykj.icm.utils.FileUtils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 升级控制器
 */
@Api(tags = "系统升级管理", description = "提供系统升级相关的接口")
@RestController
@RequestMapping("/api/upgrade")
public class UpgradeController {

    @Autowired
    private UpgradeService upgradeService;

    /**
     * 上传升级包和Md5校验
     *
     * @param file 升级包文件
     * @param request UpgradeRequest
     * @return 上传结果，包含MinIO文件信息（fileUrl、objectName、fileSize）
     */
    @ApiOperation(value = "上传升级包和Md5校验", notes = "上传系统升级包到MinIO，支持压缩文件和MD5校验，返回文件URL等信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "version", value = "升级包版本", required = true, dataTypeClass = String.class,
            paramType = "form"),
        @ApiImplicitParam(name = "request", value = "UpgradeRequest", required = true,
            dataTypeClass = UpgradeRequest.class, paramType = "form")})
    @PostMapping("/uploadUpgradePackageAndCheckMd5")
    public Result<UpgradeRequest> uploadUpgradePackageAndCheckMd5(
        @ApiParam(value = "升级包文件", required = true) @RequestPart("file") MultipartFile file, UpgradeRequest request) {
        try {
            if (request == null) {
                request = new UpgradeRequest();
            }
            // 创建升级请求
            String filename = file.getOriginalFilename();
            request.setName(filename.substring(0, filename.lastIndexOf(".")));

            // 上传升级包
            UpgradeRequest result = upgradeService.uploadUpgradePackageAndCheckMd5(file, request);
            return Result.success("上传成功", result);
        } catch (Exception e) {
            return Result.failed("上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传升级包
     * 
     * @param file 升级包文件
     * @param md5 升级包MD5值
     * @return 上传结果
     */
    @Deprecated
    @ApiOperation(value = "上传升级包", notes = "上传系统升级包，支持压缩文件，可以进行MD5校验")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "version", value = "升级包版本", required = true, dataTypeClass = String.class,
            paramType = "form"),
        @ApiImplicitParam(name = "md5", value = "文件MD5值", required = false, dataTypeClass = String.class,
            paramType = "form")})
    @PostMapping("/upload")
    public Result<String> uploadUpgradePackage(
        @ApiParam(value = "升级包文件", required = true) @RequestPart("file") MultipartFile file,
        @RequestParam(value = "md5", required = false) String md5) {

        // 创建升级请求
        UpgradeRequest request = new UpgradeRequest();
        String filename = file.getOriginalFilename();
        request.setName(filename.substring(0, filename.lastIndexOf(".")));
        request.setMd5(md5);

        // 上传升级包
        return upgradeService.uploadUpgradePackage(file, request);
    }

    /**
     * 升级包md5校验
     *
     * @param filePath 升级包文件
     * @param md5 升级包MD5值
     * @return md5校验结果
     */
    @Deprecated
    @ApiOperation(value = "升级包md5校验", notes = "升级包md5校验")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "filePath", value = "升级包地址", required = true, dataTypeClass = String.class),
        @ApiImplicitParam(name = "md5", value = "文件MD5值", required = true, dataTypeClass = String.class)})
    @GetMapping("/md5Check")
    public Result<String> md5Check(@RequestParam(value = "filePath") String filePath,
        @RequestParam(value = "md5") String md5) {

        try {
            boolean result = upgradeService.md5Check(filePath, md5);
            if (result) {
                return Result.success("校验成功");
            } else {
                return Result.failed("校验失败");
            }
        } catch (Exception e) {
            return Result.failed("校验失败: " + e.getMessage());
        }
    }

    /**
     * 升级包md5
     *
     * @param filePath 升级包文件
     * @param md5 升级包MD5值
     * @return md5校验结果
     */
    @ApiOperation(value = "升级包md5", notes = "升级包md5")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "filePath", value = "升级包地址", required = true, dataTypeClass = String.class),
        @ApiImplicitParam(name = "md5", value = "文件MD5值", required = false, dataTypeClass = String.class)})
    @GetMapping("/md5")
    public Result<Object> md5(@RequestParam(value = "filePath") String filePath,
        @RequestParam(value = "md5", required = false) String md5) {
        File file = new File(filePath);
        if (StrUtil.isBlank(md5)) {
            md5 = DigestUtils.md5Hex(FileUtil.readBytes(file));
            return Result.success(md5);
        } else {
            return Result.success(FileUtils.verifyMD5ByHutoolsAndCodec(file, md5));
        }
    }

    /**
     * 创建升级任务
     * 
     * @param request 升级任务请求参数
     * @return 创建结果
     */
    @ApiOperation(value = "创建升级任务", notes = "创建升级任务，支持立即升级和定时升级")
    @PostMapping("/create-task")
    public Result<String> createUpgradeTask(@RequestBody UpgradeRequest request) {
        try {
            // 创建升级任务
            String taskId = upgradeService.createUpgradeTask(request.getPackageId(), request);
            return Result.success("创建成功", taskId);
        } catch (Exception e) {
            return Result.failed("创建失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级包列表
     * 
     * @return 升级包列表
     */
    @ApiOperation(value = "获取升级包列表", notes = "获取所有上传的升级包列表")
    @GetMapping("/packages")
    public Result<List<UpgradePackage>> getUpgradePackages() {
        try {
            List<UpgradePackage> packages = upgradeService.getUpgradePackages();
            return Result.success(packages);
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级任务列表
     * 
     * @return 升级任务列表
     */
    @ApiOperation(value = "获取升级任务列表", notes = "获取所有升级任务列表")
    @GetMapping("/tasks")
    public Result<List<UpgradeTask>> getUpgradeTasks() {
        try {
            List<UpgradeTask> tasks = upgradeService.getUpgradeTasks();
            return Result.success(tasks);
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级包详情
     * 
     * @param id 升级包ID
     * @return 升级包详情
     */
    @ApiOperation(value = "获取升级包详情", notes = "根据ID获取升级包详情")
    @ApiImplicitParam(name = "id", value = "升级包ID", required = true, dataTypeClass = String.class, paramType = "path")
    @GetMapping("/packages/{id}")
    public Result<UpgradePackage> getUpgradePackage(@PathVariable("id") String id) {
        try {
            UpgradePackage upgradePackage = upgradeService.getUpgradePackage(id);

            if (upgradePackage != null) {
                return Result.success(upgradePackage);
            } else {
                return Result.failed("升级包不存在");
            }
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级任务详情
     * 
     * @param id 升级任务ID
     * @return 升级任务详情
     */
    @ApiOperation(value = "获取升级任务详情", notes = "根据ID获取升级任务详情")
    @ApiImplicitParam(name = "id", value = "升级任务ID", required = true, dataTypeClass = String.class, paramType = "path")
    @GetMapping("/tasks/{id}")
    public Result<UpgradeTask> getUpgradeTask(@PathVariable("id") String id) {
        try {
            UpgradeTask task = upgradeService.getUpgradeTask(id);

            if (task != null) {
                return Result.success(task);
            } else {
                return Result.failed("升级任务不存在");
            }
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级历史日志列表
     * 
     * @return 升级历史日志列表
     */
    @ApiOperation(value = "获取升级历史日志列表", notes = "获取所有升级历史记录，包括升级和回退操作，可通过operationType字段区分（0-升级，1-回退）")
    @GetMapping("/logs")
    public Result<List<UpgradeLog>> getUpgradeLogs() {
        try {
            List<UpgradeLog> logs = upgradeService.getUpgradeLogs();
            return Result.success(logs);
        } catch (Exception e) {
            return Result.failed("获取升级历史日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取升级历史日志详情
     * 
     * @param id 升级日志ID
     * @return 升级历史日志详情
     */
    @ApiOperation(value = "获取升级历史日志详情", notes = "根据ID获取升级历史日志详情，operationType字段表示操作类型（0-升级，1-回退）")
    @ApiImplicitParam(name = "id", value = "升级日志ID", required = true, dataTypeClass = String.class, paramType = "path")
    @GetMapping("/logs/{id}")
    public Result<UpgradeLog> getUpgradeLog(@PathVariable("id") String id) {
        try {
            UpgradeLog log = upgradeService.getUpgradeLog(id);

            if (log != null) {
                return Result.success(log);
            } else {
                return Result.failed("升级历史日志不存在");
            }
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 立即执行升级任务
     * 
     * @param id 升级任务ID
     * @return 执行结果
     */
    @ApiOperation(value = "立即执行升级任务", notes = "立即执行指定ID的升级任务")
    @ApiImplicitParam(name = "id", value = "升级任务ID", required = true, dataTypeClass = String.class, paramType = "path")
    @PostMapping("/tasks/{id}/execute")
    public Result<String> executeUpgradeTask(@PathVariable("id") String id) {
        try {
            upgradeService.executeUpgrade(id);
            return Result.success("执行成功");
        } catch (Exception e) {
            return Result.failed("执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前版本信息
     * 
     * @return 当前版本信息
     */
    @ApiOperation(value = "获取当前版本信息", notes = "获取系统当前使用的版本信息")
    @GetMapping("/current")
    public Result<UpgradePackage> getCurrentUpgradePackage() {
        try {
            UpgradePackage currentPackage = upgradeService.getCurrentUpgradePackage();

            if (currentPackage != null) {
                return Result.success(currentPackage);
            } else {
                return Result.failed("未找到当前版本信息");
            }
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取上一个版本信息
     * 
     * @return 上一个版本信息
     */
    @ApiOperation(value = "获取上一个版本信息", notes = "获取系统上一个使用的版本信息，用于版本回退")
    @GetMapping("/previous")
    public Result<UpgradePackage> getPreviousUpgradePackage() {
        try {
            UpgradePackage previousPackage = upgradeService.getPreviousUpgradePackage();

            if (previousPackage != null) {
                return Result.success(previousPackage);
            } else {
                return Result.failed("未找到上一个版本信息");
            }
        } catch (Exception e) {
            return Result.failed("获取失败: " + e.getMessage());
        }
    }

    /**
     * 取消定时升级任务
     * 
     * @param taskId 任务ID
     * @return 操作结果
     */
    @ApiOperation(value = "取消定时升级任务", notes = "取消指定的定时升级任务")
    @ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataTypeClass = String.class,
        paramType = "path")
    @PostMapping("/tasks/{taskId}/cancel")
    public Result<String> cancelScheduledTask(@PathVariable("taskId") String taskId) {
        try {
            upgradeService.cancelScheduledTask(taskId);
            return Result.success("取消成功");
        } catch (Exception e) {
            return Result.failed("取消失败: " + e.getMessage());
        }
    }

    /**
     * 删除升级包
     * 
     * @param packageId 升级包ID
     * @return 操作结果
     */
    @ApiOperation(value = "删除升级包", notes = "删除指定的升级包，如果有相关的定时任务会被取消")
    @ApiImplicitParam(name = "packageId", value = "升级包ID", required = true, dataTypeClass = String.class,
        paramType = "path")
    @DeleteMapping("/packages/{packageId}")
    public Result<String> deleteUpgradePackage(@PathVariable("packageId") String packageId) {
        try {
            upgradeService.deleteUpgradePackage(packageId);
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.failed("删除失败: " + e.getMessage());
        }
    }

    /**
     * 版本回退
     * 
     * @return 回退操作结果
     */
    @ApiOperation(value = "版本回退", notes = "自动分析当前版本包含的文件，回退到对应的上一个版本，会重启服务器")
    @PostMapping("/rollback")
    public Result<String> rollbackUpgrade() {
        try {
            return upgradeService.rollbackUpgrade();
        } catch (Exception e) {
            return Result.failed("回退操作失败: " + e.getMessage());
        }
    }

    /**
     * 按文件回退版本
     * 
     * @param fileName 需要回退的文件名，例如"icm-xxx"
     * @return 回退操作结果
     */
    @ApiOperation(value = "按文件回退版本", notes = "回退指定文件到包含该文件的上一个版本，会重启服务器")
    @ApiImplicitParam(name = "fileName", value = "文件名称，例如'icm-xxx'", required = true, dataTypeClass = String.class)
    @PostMapping("/rollback/file")
    public Result<String> rollbackUpgradeByFile(@RequestParam String fileName) {
        try {
            upgradeService.rollbackUpgradeByFile(fileName);
            return Result.success("文件回退操作已执行，服务器将在10秒后重启");
        } catch (Exception e) {
            return Result.failed("文件回退操作失败: " + e.getMessage());
        }
    }
}
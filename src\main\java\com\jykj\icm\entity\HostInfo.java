package com.jykj.icm.entity;

import lombok.Data;

@Data
public class HostInfo {
    /**
     * ID
     */
    private String id;
    /**
     * 本地机器类型（主/备）
     */
    private String hostType;
    /**
     * 本地机器IP
     */
    private String localIp;
    /**
     * 本地机器用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 远程主机IP
     */
    private String remoteIp;
    /**
     * 远程主机用户名
     */
    private String remoteUsername;
    /**
     * 远程主机密码
     */
    private String remotePassword;
    /**
     * 数据库密码（主机类型为主时有效）
     */
    private String dbPassword;
    /**
     * 网卡（ens33）
     */
    private String networkCard;
    /**
     * 虚拟IP
     */
    private String vipIp;

}

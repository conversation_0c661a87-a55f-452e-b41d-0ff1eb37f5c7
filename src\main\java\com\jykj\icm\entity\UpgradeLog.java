package com.jykj.icm.entity;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 升级历史日志实体类
 */
@ApiModel(value = "升级历史日志", description = "系统升级历史记录信息")
public class UpgradeLog {
    /**
     * 唯一ID
     */
    @ApiModelProperty(value = "唯一ID", example = "20250331173915064")
    private String id;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "升级包文件名称", example = "icm-system-upgrade-v1.2.3.zip")
    private String fileName;

    /**
     * 来源版本
     */
    @ApiModelProperty(value = "来源版本", example = "1.1.0")
    private String fromVersion;

    /**
     * 目标版本
     */
    @ApiModelProperty(value = "目标版本", example = "1.2.3")
    private String toVersion;

    /**
     * 升级时间
     */
    @ApiModelProperty(value = "升级时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date upgradeTime;

    /**
     * 升级人员
     */
    @ApiModelProperty(value = "升级操作人员", example = "admin")
    private String operator;

    /**
     * 升级结果：0-成功，1-失败
     */
    @ApiModelProperty(value = "升级结果：0-成功，1-失败", example = "0")
    private Integer result;

    /**
     * 错误信息，当result=1时有效
     */
    @ApiModelProperty(value = "错误信息", notes = "当result=1时有效", example = "升级过程中断")
    private String errorMessage;

    /**
     * 备注信息
     */
    @ApiModelProperty(value = "备注信息", example = "常规版本升级")
    private String remark;

    /**
     * 关联的任务ID
     */
    @ApiModelProperty(value = "关联的任务ID", example = "20250331173915064")
    private String taskId;

    /**
     * 关联的升级包ID
     */
    @ApiModelProperty(value = "关联的升级包ID", example = "20250331173915064")
    private String packageId;

    /**
     * 操作类型：0-升级，1-回退
     */
    @ApiModelProperty(value = "操作类型：0-升级，1-回退", example = "0")
    private Integer operationType = 0;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFromVersion() {
        return fromVersion;
    }

    public void setFromVersion(String fromVersion) {
        this.fromVersion = fromVersion;
    }

    public String getToVersion() {
        return toVersion;
    }

    public void setToVersion(String toVersion) {
        this.toVersion = toVersion;
    }

    public Date getUpgradeTime() {
        return upgradeTime;
    }

    public void setUpgradeTime(Date upgradeTime) {
        this.upgradeTime = upgradeTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }
}
package com.jykj.icm.entity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 升级包实体类
 */
@ApiModel(value = "升级包信息", description = "系统升级包详细信息")
public class UpgradePackage {
    /**
     * 唯一ID
     */
    @ApiModelProperty(value = "唯一ID", example = "20250331173915064")
    private String id;

    /**
     * 升级包名称
     */
    @ApiModelProperty(value = "升级包名称", example = "系统核心功能升级包")
    private String name;

    /**
     * 升级包版本
     */
    @ApiModelProperty(value = "升级包版本", example = "1.2.3")
    private String version;

    /**
     * 升级包MD5值，用于校验文件完整性
     */
    @ApiModelProperty(value = "升级包MD5值", example = "a1b2c3d4e5f6g7h8i9j0")
    private String md5;

    /**
     * 包含的文件列表，key为文件名，value为文件路径
     */
    @ApiModelProperty(value = "包含的文件列表")
    private Map<String, String> files = new HashMap<>();

    /**
     * 是否强关联
     */
    @ApiModelProperty(value = "是否强关联", example = "false")
    private Boolean forceAssociation = false;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /**
     * 上传者
     */
    @ApiModelProperty(value = "上传者", example = "admin")
    private String uploader;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息", example = "修复了系统Bug，提升了性能")
    private String description;

    /**
     * 存储路径（本地路径，兼容旧版本）
     */
    @ApiModelProperty(value = "存储路径", example = "/home/<USER>/upgrade/e5a7cfb3-b25c-4c6a-b8f2-3f8a0d4a9876")
    private String storagePath;

    /**
     * MinIO文件URL
     */
    @ApiModelProperty(value = "MinIO文件URL", example = "http://localhost:9000/icm-files/upgrade-packages/20250331173915064/package.zip")
    private String fileUrl;

    /**
     * MinIO对象名称
     */
    @ApiModelProperty(value = "MinIO对象名称", example = "upgrade-packages/20250331173915064/package.zip")
    private String objectName;

    /**
     * 文件大小（字节）
     */
    @ApiModelProperty(value = "文件大小", example = "1048576")
    private Long fileSize;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public Map<String, String> getFiles() {
        return files;
    }

    public void setFiles(Map<String, String> files) {
        this.files = files;
    }

    public Boolean getForceAssociation() {
        return forceAssociation;
    }

    public void setForceAssociation(Boolean forceAssociation) {
        this.forceAssociation = forceAssociation;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getUploader() {
        return uploader;
    }

    public void setUploader(String uploader) {
        this.uploader = uploader;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
}
package com.jykj.icm.entity;

import java.util.Date;
import java.util.Map;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 升级请求DTO
 */
@ApiModel(value = "升级请求信息", description = "系统升级请求参数")
public class UpgradeRequest {
    /**
     * 升级包ID
     */
    @ApiModelProperty(value = "升级包ID", required = true, example = "20250331173915064")
    private String packageId;

    /**
     * 升级包名称
     */
    @ApiModelProperty(value = "升级包名称", required = true, example = "系统核心功能升级包")
    private String name;

    /**
     * 升级包版本
     */
    @ApiModelProperty(value = "升级包版本", required = true, example = "1.2.3")
    private String version;

    /**
     * 升级包MD5值
     */
    @ApiModelProperty(value = "升级包MD5值", required = false, example = "a1b2c3d4e5f6g7h8i9j0")
    private String md5;

    /**
     * 包含的文件列表
     */
    @ApiModelProperty(value = "包含的文件列表", notes = "key为文件名，value为文件路径")
    private Map<String, String> files;

    /**
     * 是否强关联
     */
    @ApiModelProperty(value = "是否强关联", example = "false")
    private Boolean forceAssociation;

    /**
     * 升级类型：0-立即升级，1-定时升级
     */
    @ApiModelProperty(value = "升级类型", notes = "0-立即升级，1-定时升级", example = "0")
    private Integer upgradeType;

    /**
     * 计划升级时间，当upgradeType=1时有效
     */
    @ApiModelProperty(value = "计划升级时间", notes = "当upgradeType=1时有效", example = "2023-05-20 14:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scheduledTime;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息", example = "修复了系统Bug，提升了性能")
    private String description;

    /**
     * MinIO文件URL
     */
    @ApiModelProperty(value = "MinIO文件URL", example = "http://localhost:9000/icm-files/upgrade-packages/20250331173915064/package.zip")
    private String fileUrl;

    /**
     * MinIO对象名称
     */
    @ApiModelProperty(value = "MinIO对象名称", example = "upgrade-packages/20250331173915064/package.zip")
    private String objectName;

    /**
     * 文件大小（字节）
     */
    @ApiModelProperty(value = "文件大小", example = "1048576")
    private Long fileSize;

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public Map<String, String> getFiles() {
        return files;
    }

    public void setFiles(Map<String, String> files) {
        this.files = files;
    }

    public Boolean getForceAssociation() {
        return forceAssociation;
    }

    public void setForceAssociation(Boolean forceAssociation) {
        this.forceAssociation = forceAssociation;
    }

    public Integer getUpgradeType() {
        return upgradeType;
    }

    public void setUpgradeType(Integer upgradeType) {
        this.upgradeType = upgradeType;
    }

    public Date getScheduledTime() {
        return scheduledTime;
    }

    public void setScheduledTime(Date scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
}
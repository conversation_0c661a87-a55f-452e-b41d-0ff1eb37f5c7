package com.jykj.icm.entity;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 升级任务实体类
 */
@ApiModel(value = "升级任务信息", description = "系统升级任务详细信息")
public class UpgradeTask {
    /**
     * 唯一ID
     */
    @ApiModelProperty(value = "唯一ID", example = "20250331173915064")
    private String id;

    /**
     * 关联的升级包ID
     */
    @ApiModelProperty(value = "关联的升级包ID", example = "20250331173915064")
    private String upgradePackageId;

    /**
     * 升级类型：0-立即升级，1-定时升级
     */
    @ApiModelProperty(value = "升级类型：0-立即升级，1-定时升级", example = "0")
    private Integer upgradeType;

    /**
     * 计划升级时间，当upgradeType=1时有效
     */
    @ApiModelProperty(value = "计划升级时间", notes = "当upgradeType=1时有效")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date scheduledTime;

    /**
     * 升级状态：0-待执行，1-执行中，2-已完成，3-失败，4-已取消
     */
    @ApiModelProperty(value = "升级状态：0-待执行，1-执行中，2-已完成，3-失败，4-已取消", example = "0")
    private Integer status;

    /**
     * 任务创建时间
     */
    @ApiModelProperty(value = "任务创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 任务创建者
     */
    @ApiModelProperty(value = "任务创建者", example = "admin")
    private String creator;

    /**
     * 实际执行时间
     */
    @ApiModelProperty(value = "实际执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date executeTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "系统常规升级")
    private String remark;

    /**
     * 错误信息，当status=3时有效
     */
    @ApiModelProperty(value = "错误信息", notes = "当status=3时有效", example = "升级过程中断")
    private String errorMessage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUpgradePackageId() {
        return upgradePackageId;
    }

    public void setUpgradePackageId(String upgradePackageId) {
        this.upgradePackageId = upgradePackageId;
    }

    public Integer getUpgradeType() {
        return upgradeType;
    }

    public void setUpgradeType(Integer upgradeType) {
        this.upgradeType = upgradeType;
    }

    public Date getScheduledTime() {
        return scheduledTime;
    }

    public void setScheduledTime(Date scheduledTime) {
        this.scheduledTime = scheduledTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
package com.jykj.icm.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "用户信息")
public class User {
    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("角色")
    private String role;

    @ApiModelProperty("是否需要修改密码")
    private boolean needChangePassword;

    @ApiModelProperty("是否为超级管理员")
    private boolean superAdmin;
}
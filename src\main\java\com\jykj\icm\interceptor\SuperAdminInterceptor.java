package com.jykj.icm.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jykj.icm.annotation.RequireSuperAdmin;
import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.User;

/**
 * 超级管理员权限校验拦截器
 */
@Component
public class SuperAdminInterceptor implements HandlerInterceptor {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod)handler;
        RequireSuperAdmin requireSuperAdmin = handlerMethod.getMethodAnnotation(RequireSuperAdmin.class);

        if (requireSuperAdmin == null) {
            return true;
        }

        // 从请求属性中获取用户信息，这是由TokenInterceptor设置的
        User user = (User)request.getAttribute("user");

        if (user == null || !user.isSuperAdmin()) {
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            Result<?> result = Result.failed("the operation requires super admin permission");
            response.getWriter().write(objectMapper.writeValueAsString(result));
            return false;
        }

        return true;
    }
}
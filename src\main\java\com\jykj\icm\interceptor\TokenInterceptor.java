package com.jykj.icm.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jykj.icm.common.result.Result;
import com.jykj.icm.entity.User;
import com.jykj.icm.utils.TokenManager;

/**
 * Token拦截器 拦截请求并验证token，确保用户已登录
 */
@Component
public class TokenInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenManager tokenManager;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 请求处理前拦截
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");

        // 如果token存在并且有效
        if (token != null && tokenManager.validateToken(token)) {
            // 获取用户信息并存入请求属性中，供后续使用
            User user = tokenManager.getUserByToken(token);
            request.setAttribute("user", user);

            // 自动刷新token过期时间
            tokenManager.refreshToken(token);

            return true;
        }

        // token无效，返回401未授权
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.getWriter().write(objectMapper.writeValueAsString(Result.unauthorized()));
        return false;
    }
}
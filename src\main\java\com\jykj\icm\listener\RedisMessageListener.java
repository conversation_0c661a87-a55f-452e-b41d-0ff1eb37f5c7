package com.jykj.icm.listener;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import com.jykj.icm.common.Constants;
import com.jykj.icm.entity.HostInfo;
import com.jykj.icm.service.HostConfigServer;
import com.jykj.icm.utils.RedisUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class RedisMessageListener implements MessageListener {
    private final HostConfigServer hostConfigServer;
    private final RedisUtils redisUtils;
    private volatile ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private volatile AtomicBoolean isSubscribed = new AtomicBoolean(false);
    private static final int INITIAL_DELAY = 5;
    private static final int RETRY_INTERVAL = 10;

    @PostConstruct
    public void init() {
        startSubscriptionRetry();
    }

    @PreDestroy
    public void destroy() {
        try {
            scheduler.shutdown();
            if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 重置订阅状态并重新开始尝试订阅
     */
    public void resetSubscription() {
        isSubscribed.set(false);
        if (scheduler.isShutdown()) {
            // 如果调度器已关闭，创建新的调度器
            scheduler = Executors.newSingleThreadScheduledExecutor();
            startSubscriptionRetry();
        }
        log.info("Redis 订阅已重置，将重试订阅。");
    }

    private void startSubscriptionRetry() {
        scheduler.scheduleWithFixedDelay(this::trySubscribe, INITIAL_DELAY, RETRY_INTERVAL, TimeUnit.SECONDS);
    }

    private void trySubscribe() {
        if (isSubscribed.get()) {
            scheduler.shutdown();
            return;
        }

        try {
            HostInfo hostInfo = hostConfigServer.getHostConfig(null);
            if (hostInfo != null && hostInfo.getHostType() != null) {
                String redisHost = hostInfo.getLocalIp();
                if (redisHost != null && !redisHost.isEmpty()) {
                    redisUtils.subscribe(redisHost, Constants.REDIS_UPDATE_NOTICE_KEY, this);
                    isSubscribed.set(true);
                    log.info("已成功订阅主机 {} 上的 Redis 更新通知。", redisHost);
                } else {
                    log.warn("Redis主机为空或未设置。将重试...");
                }
            } else {
                log.warn("主机配置尚未可用。将重试...");
            }
        } catch (Exception e) {
            log.error("尝试订阅 Redis 时出错：{}。将重试...", e.getMessage());
        }
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String channelName = new String(message.getChannel());
            String body = new String(message.getBody());
            log.info("收到 Redis 消息 - 频道: {}, 消息: {}", channelName, body);
            if (Constants.REDIS_UPDATE_NOTICE_KEY.equals(channelName)) {
                hostConfigServer.updateConfig(body);
            }
        } catch (Exception e) {
            log.error("处理 Redis 消息时出错", e);
        }
    }
}
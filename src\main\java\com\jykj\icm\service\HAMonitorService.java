package com.jykj.icm.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jykj.icm.common.Constants;
import com.jykj.icm.common.exception.BusinessException;
import com.jykj.icm.entity.HostInfo;
import com.jykj.icm.listener.RedisMessageListener;
import com.jykj.icm.utils.RedisUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 主机：从sentinel里面获取主机IP,并记录到redis中 1、如果主机变更，切换到备机
 * 3、主机直接改为primary，其他备机机器：修改MODIFY_REPL_CONN_INFO配置文件，然后重启（或者直接删除data目录，重新构建opengauss）
 * 4、持续检测老主机，如果恢复，通知其修改为备机
 * 5、同时还需要修改统一门户的数据库配置文件，并重启统一门户（部署统一门户时，将配置文件挂载，或者使用docker run 命令修改）
 */
@Slf4j
@Service
public class HAMonitorService implements InitializingBean {
    private final ScheduledExecutorService scheduler;
    private final HostConfigServer hostConfigServer;
    private final RedisUtils redisUtils;
    private static volatile boolean isInitialized = false;

    @Autowired
    private JsonDbManager jsonDbsonManager;
    @Autowired
    private RedisMessageListener redisMessageListener;

    public HAMonitorService(HostConfigServer hostConfigServer, RedisUtils redisUtils) throws Exception {
        this.hostConfigServer = hostConfigServer;
        this.redisUtils = redisUtils;
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
    }

    public void startMonitoring() {
        if (isInitialized) {
            return;
        }
        log.info("开始检查集群状态");
        // 优化：从30秒改为60秒，减少一半的检查频率
        scheduler.scheduleAtFixedRate(this::checkClusterStatus, 0, 60, TimeUnit.SECONDS);
    }

    // 加锁 todo
    private void checkClusterStatus() {
        try {
            HostInfo hostConfig = hostConfigServer.getHostConfig(null);
            if (hostConfig == null || StrUtil.isBlank(hostConfig.getDbPassword())) {
                log.debug("主机配置不完整，跳过集群状态检查"); // 优化：降低日志级别，减少日志输出
                return;
            }

            // 优化：增加超时控制，避免长时间阻塞
            String masterIp = hostConfigServer.getMasterIpFromSentinelOrLatestLiveIp(hostConfig);
            if (StrUtil.isBlank(masterIp)) {
                log.warn("无法从哨兵获取主IP，将在下次检查时重试");
                return;
            }

            String masterIpOld = hostConfigServer.getMasterIp(hostConfig);
            log.debug("检查集群状态 - 旧主IP: {}, 新主IP: {}", masterIpOld, masterIp);

            if (Objects.equals(masterIpOld, masterIp)) {
                log.debug("集群状态无变化，跳过部署操作"); // 优化：减少不必要的minio检查频率
                // 优化：每5次检查才执行一次minio检查，减少系统调用
                if (System.currentTimeMillis() % 300000 < 60000) { // 每5分钟检查一次minio
                    dualMinio(hostConfig, masterIp);
                }
                return;
            }
            List<String> list = hostConfigServer.getHostKeyList(hostConfig);
            if (Objects.equals(hostConfig.getLocalIp(), masterIp)) {
                // 之前是备机，修改为主机
                hostConfig.setHostType(Constants.HOST_TYPE_MASTER);
                hostConfigServer.deployGaussMaster(hostConfig, list);
                hostConfigServer.deployKeepalivedMaster(hostConfig);
                hostConfigServer.deployNginxMaster(hostConfig, list);
                //minio 服务
                hostConfigServer.deployMinioMaster(hostConfig, list);
            } else {
                // 之前是主机，修改为备机
                hostConfig.setRemoteIp(masterIp);
                if (Constants.HOST_TYPE_MASTER.equals(hostConfig.getHostType())) {
                    hostConfig.setHostType(Constants.HOST_TYPE_SLAVE);
                }
                hostConfigServer.deployGaussSlave(hostConfig);
                hostConfigServer.deployKeepalivedSlave(hostConfig);
                hostConfigServer.deployNginxMaster(hostConfig, list);
                //minio 服务
                hostConfigServer.deployMinioSlave(hostConfig, list);
            }
            // redis也要修改sentinel monitor mymaster *************** 6379 1

            // 重置redis订阅
            redisMessageListener.resetSubscription();
            // hostInfo转化为Map
            Map<String, Object> map = BeanUtil.beanToMap(hostConfig);
            jsonDbsonManager.updateRow(hostConfig.getId(), map);
            // 异步等待 Redis 启动并写入配置
            CompletableFuture.runAsync(() -> {
                redisUtils.redisSet(masterIp, Constants.REDIS_MASTER_IP_KEY, masterIp);
                redisUtils.redisSet(masterIp, Constants.REDIS_HOSTINFO_KEY + "::" + hostConfig.getLocalIp(),
                        JSONUtil.toJsonStr(hostConfig));
            });
        } catch (Exception e) {
            log.error("检查集群状态错误: {}", e.getMessage());
            // 优化：避免打印完整的异常堆栈，减少日志量
            if (log.isDebugEnabled()) {
                log.debug("完整的异常信息:", e);
            }

            // 优化：在异常情况下释放可能的资源
            try {
                System.gc(); // 建议垃圾回收，释放内存
            } catch (Exception ignored) {
                // 忽略垃圾回收异常
            }
        }
    }

    private void dualMinio(HostInfo hostConfig, String masterIp) {
        List<String> list = hostConfigServer.getHostKeyList(hostConfig);
        if (Objects.equals(hostConfig.getLocalIp(), masterIp)) {
            // 之前是备机，修改为主机
            // minio 服务
            hostConfigServer.deployMinioMaster(hostConfig, list);
        } else {
            // 之前是主机，修改为备机
            // minio 服务
            hostConfigServer.deployMinioSlave(hostConfig, list);
        }
    }

    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private void executeCommand(String... command) throws IOException, InterruptedException {
        Process process = new ProcessBuilder(command).start();
        consumeOutputStream(process);
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new BusinessException("命令执行失败: " + String.join(" ", command));
        }
    }

    private void consumeOutputStream(Process process) throws IOException {
        // 创建有名称的守护线程
        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("[Process Output] {}", line);
                }
            } catch (IOException e) {
                log.error("读取进程输出流时出错", e);
            }
        }, "Process-Output-Consumer");

        // 单独处理错误流
        Thread errorThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.error("[Process Error] {}", line);
                }
            } catch (IOException e) {
                log.error("读取进程错误流时出错", e);
            }
        }, "Process-Error-Consumer");

        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        startMonitoring();
        isInitialized = true;
    }
}
package com.jykj.icm.service;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class JsonDbManager {
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long LOCK_TIMEOUT_MS = 5000;
    private static final int BUFFER_SIZE = 8192;

    @Value("${json.db.windows.path:C:/home/<USER>/data.json}")
    private String windowsFilePath;

    @Value("${json.db.linux.path: /home/<USER>/icm/data.json}")
    private String linuxFilePath;

    private String filePath;
    private final ObjectMapper objectMapper;
    private final ObjectWriter objectWriter;
    private File jsonFile;

    public JsonDbManager() {
        this.objectMapper = new ObjectMapper();
        this.objectWriter = objectMapper.writer().withDefaultPrettyPrinter();
    }

    @PostConstruct
    public void init() {
        // 根据操作系统选择合适的文件路径
        String os = System.getProperty("os.name").toLowerCase();
        this.filePath = os.contains("win") ? windowsFilePath : linuxFilePath;
        log.info("操作系统：{}，使用的文件路径：{}", os, this.filePath);

        this.jsonFile = initializeJsonFile();
    }

    private File initializeJsonFile() {
        File file = new File(filePath);
        File parentDir = file.getParentFile();

        try {
            // 确保目录存在
            if (!parentDir.exists() && !parentDir.mkdirs()) {
                throw new IOException("创建目录失败： " + parentDir.getAbsolutePath());
            }

            // 如果文件不存在，创建一个空的JSON文件
            if (!file.exists() && !file.createNewFile()) {
                throw new IOException("创建文件失败： " + file.getAbsolutePath());
            }

            // 如果文件为空，初始化为空数组
            if (file.length() == 0) {
                try (RandomAccessFile randomAccessFile = new RandomAccessFile(file, "rw")) {
                    objectWriter.writeValue(randomAccessFile, new ArrayList<>());
                }
            }

            return file;
        } catch (IOException e) {
            log.error("初始化 JSON 文件失败：{}", e.getMessage());
            throw new JsonDbException("初始化 JSON 文件失败", e);
        }
    }

    @Retryable(maxAttempts = MAX_RETRY_ATTEMPTS, backoff = @Backoff(delay = 1000))
    public synchronized List<Map<String, Object>> readData() {
        try (RandomAccessFile file = new RandomAccessFile(jsonFile, "r"); FileChannel channel = file.getChannel()) {

            // 获取共享锁，设置超时
            FileLock lock = tryAcquireLock(channel, true);
            if (lock == null) {
                throw new JsonDbException("超时未能获取读锁");
            }

            try {
                if (channel.size() == 0) {
                    return new ArrayList<>();
                }

                // 使用缓冲区读取大文件
                byte[] data = readFileWithBuffer(file, (int)channel.size());
                return objectMapper.readValue(data, new TypeReference<List<Map<String, Object>>>() {});
            } finally {
                lock.release();
            }
        } catch (IOException e) {
            log.error("读取数据失败：{}", e.getMessage());
            throw new JsonDbException("读取数据失败", e);
        }
    }

    @Retryable(maxAttempts = MAX_RETRY_ATTEMPTS, backoff = @Backoff(delay = 1000))
    public synchronized void writeData(List<Map<String, Object>> data) {
        try (RandomAccessFile file = new RandomAccessFile(jsonFile, "rw"); FileChannel channel = file.getChannel()) {

            // 获取独占锁，设置超时
            FileLock lock = tryAcquireLock(channel, false);
            if (lock == null) {
                throw new JsonDbException("超时未能获取写锁");
            }

            try {
                // 清空文件内容
                channel.truncate(0);
                // 写入新数据
                objectWriter.writeValue(file, data);
            } finally {
                lock.release();
            }
        } catch (IOException e) {
            log.error("写入数据失败：{}", e.getMessage());
            throw new JsonDbException("写入数据失败", e);
        }
    }

    private FileLock tryAcquireLock(FileChannel channel, boolean shared) throws IOException {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < LOCK_TIMEOUT_MS) {
            try {
                FileLock lock = channel.tryLock(0L, Long.MAX_VALUE, shared);
                if (lock != null) {
                    return lock;
                }
            } catch (IOException e) {
                // 继续尝试
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new JsonDbException("锁获取被中断");
            }
        }
        return null;
    }

    private byte[] readFileWithBuffer(RandomAccessFile file, int fileSize) throws IOException {
        byte[] data = new byte[fileSize];
        byte[] buffer = new byte[BUFFER_SIZE];
        int bytesRead;
        int position = 0;

        while (position < fileSize
            && (bytesRead = file.read(buffer, 0, Math.min(buffer.length, fileSize - position))) != -1) {
            System.arraycopy(buffer, 0, data, position, bytesRead);
            position += bytesRead;
        }

        return data;
    }

    // 添加新行
    public synchronized void addRow(Map<String, Object> row) {
        List<Map<String, Object>> data = readData();
        data.add(row);
        writeData(data);
    }

    // 修改行
    public synchronized void updateRow(String id, Map<String, Object> newRow) {
        List<Map<String, Object>> data = readData();
        boolean found = false;
        for (Map<String, Object> row : data) {
            if (Objects.equals(row.get("id"), id)) {
                row.putAll(newRow);
                found = true;
                break;
            }
        }
        if (!found) {
            // 如果未找到匹配的行，则添加新行
            data.add(newRow);
        }
        writeData(data);
    }

    // 删除行
    public synchronized void deleteRow(String id) {
        List<Map<String, Object>> data = readData();
        data.removeIf(row -> Objects.equals(row.get("id"), id));
        writeData(data);
    }
}

// 自定义异常类
class JsonDbException extends RuntimeException {
    public JsonDbException(String message) {
        super(message);
    }

    public JsonDbException(String message, Throwable cause) {
        super(message, cause);
    }
}

package com.jykj.icm.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import com.jykj.icm.common.exception.BusinessException;
import com.jykj.icm.entity.ChangePasswordRequest;
import com.jykj.icm.entity.LoginRequest;
import com.jykj.icm.entity.User;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserService {

    @Value("${user.config.path:config/users.yml}")
    private String userConfigPath;

    private Map<String, User> users = new HashMap<>();

    @PostConstruct
    public void init() {
        loadUsers();
    }

    public User login(LoginRequest request) {
        User user = users.get(request.getUsername());
        if (user != null && user.getPassword().equals(md5(request.getPassword()))) {
            return user;
        }
        return null;
    }

    public void changePassword(String username, ChangePasswordRequest request) {
        User user = users.get(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 超级管理员的密码不允许修改，跳过密码支持
        if (user.isSuperAdmin() && request != null) {
            throw new BusinessException("超级管理员密码不允许修改");
        }
        if (request != null) {
            user.setPassword(md5(request.getNewPassword()));
        }
        user.setNeedChangePassword(false);
        saveUsers();
    }

    private void loadUsers() {
        // 首先尝试从外部配置文件加载
        File configFile = new File(userConfigPath);
        if (!configFile.exists()) {
            // 如果外部配置文件不存在，从类路径加载默认配置
            try (InputStream input = getClass().getResourceAsStream("/users.yml")) {
                // 确保配置目录存在
                File configDir = configFile.getParentFile();
                if (!configDir.exists()) {
                    configDir.mkdirs();
                }
                // 复制默认配置到外部文件
                Files.copy(input, configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                log.info("已在以下位置创建外部用户配置文件：{}", configFile.getAbsolutePath());
            } catch (IOException e) {
                log.error("创建外部用户配置失败", e);
                return;
            }
        }

        try (FileInputStream input = new FileInputStream(configFile)) {
            Yaml yaml = new Yaml();
            Map<String, Map<String, Map<String, Object>>> data = yaml.load(input);
            Map<String, Map<String, Object>> usersMap = data.get("users");

            for (Map.Entry<String, Map<String, Object>> entry : usersMap.entrySet()) {
                User user = new User();
                user.setUsername((String)entry.getValue().get("username"));
                user.setPassword((String)entry.getValue().get("password"));
                user.setRole((String)entry.getValue().get("role"));
                user.setNeedChangePassword((Boolean)entry.getValue().getOrDefault("needChangePassword", false));
                user.setSuperAdmin((Boolean)entry.getValue().getOrDefault("superAdmin", false));
                users.put(user.getUsername(), user);
            }
            log.info("已成功从以下位置加载用户配置：{}", configFile.getAbsolutePath());
        } catch (IOException e) {
            log.error("加载用户配置失败", e);
        }
    }

    private void saveUsers() {
        log.info("用户配置文件地址：" + userConfigPath);
        File configFile = new File(userConfigPath);
        Map<String, Map<String, Map<String, Object>>> data = new HashMap<>();
        Map<String, Map<String, Object>> usersMap = new HashMap<>();

        for (User user : users.values()) {
            Map<String, Object> userMap = new HashMap<>();
            userMap.put("username", user.getUsername());
            userMap.put("password", user.getPassword());
            userMap.put("role", user.getRole());
            userMap.put("needChangePassword", user.isNeedChangePassword());
            userMap.put("superAdmin", user.isSuperAdmin());
            usersMap.put(user.getUsername(), userMap);
        }

        data.put("users", usersMap);

        try {
            // 确保配置目录存在
            File configDir = configFile.getParentFile();
            if (!configDir.exists()) {
                configDir.mkdirs();
            }

            // 使用临时文件来保证写入的原子性
            File tempFile = File.createTempFile("users", ".yml", configDir);
            try (FileWriter writer = new FileWriter(tempFile)) {
                new Yaml().dump(data, writer);
            }

            // 原子性地替换原文件
            Files.move(tempFile.toPath(), configFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            log.info("已成功将用户配置保存到：{}", configFile.getAbsolutePath());
        } catch (IOException e) {
            log.error("保存用户配置失败", e);
            throw new RuntimeException("保存用户配置失败", e);
        }
    }

    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();

            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
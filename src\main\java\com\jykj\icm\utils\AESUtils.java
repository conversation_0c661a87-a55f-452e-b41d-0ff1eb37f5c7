package com.jykj.icm.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * AES加密解密工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 22/4/20 14:20
 */
public class AESUtils {

    /**
     * base64格式的默认秘钥 也可以每次生成一个随机的秘钥,使用如下代码 byte[] key =
     * SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded(); String secret = Base64.encode(key);
     */
    private static final String BASE64_SECRET = "SnlrajE5OTRKeWtqMTk5NA==";

    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个aes对象
     */
    private final static AES aes = SecureUtil.aes(SECRET_BYTES);

    /**
     * 使用aes加密
     *
     * @param content
     * @return 加密后的字符串
     */
    public static String AESEnCode(String content) {
        // 加密完以后是十六进制的
        return aes.encryptHex(content);
    }

    /**
     * 使用aes算法,进行解密
     *
     * @param ciphertext
     * @return 解密后的字符串
     */
    public static String AESDeCode(String ciphertext) {
        return aes.decryptStr(ciphertext);
    }

    /**
     * 判断是否是aes加密的字符串
     *
     * @param defaultPassword 加密后的字符串
     * @return true:是aes加密的字符串,false:不是aes加密的字符串
     */
    public static boolean isAESEnCode(String defaultPassword) {
        try {
            AESDeCode(defaultPassword);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) {
        String string = "hello world";
        String enCode = AESEnCode(string);
        String dnCode = AESDeCode(enCode);
    }
}

/**
 * @Title : ExecuteUtils
 * @Package : com.jykj.icm.utils
 * @Description : 命令执行工具类
 * @Author: 黄杰
 * @Date: 2025-04-01 11:14
 */
package com.jykj.icm.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

import com.jykj.icm.common.exception.BusinessException;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 命令执行工具类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2025-04-01
 */
@Slf4j
public class ExecuteUtils {

    @SneakyThrows
    public static void executeCommand(String... command) {
        String fullCommand = String.join(" ", command);
        log.info("完整命令：{}", fullCommand);
        Process process = new ProcessBuilder(command).start();
        consumeOutputStream(process);
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new BusinessException("命令执行失败: " + String.join(" ", command));
        }
    }

    private static void consumeOutputStream(Process process) {
        // 创建有名称的守护线程
        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader =
                new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info("[Process Output] {}", line);
                }
            } catch (IOException e) {
                log.error("读取进程输出流时出错", e);
            }
        }, "Process-Output-Consumer");

        // 单独处理错误流
        Thread errorThread = new Thread(() -> {
            try (BufferedReader reader =
                new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.error("[Process Error] {}", line);
                }
            } catch (IOException e) {
                log.error("读取进程错误流时出错", e);
            }
        }, "Process-Error-Consumer");

        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }

    public static String executeCommandAndReturnPid(String... command) throws Exception {
        String fullCommand = String.join(" ", command);
        log.info("完整命令：{}", fullCommand);

        // 使用 /bin/sh -c 执行复杂命令（如包含管道的命令）
        ProcessBuilder processBuilder = new ProcessBuilder("/bin/sh", "-c", fullCommand);
        Process process = processBuilder.start();

        // 读取命令输出
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        String pid = null;

        // 解析输出，提取 PID
        while ((line = reader.readLine()) != null) {
            if (line.contains("pid=")) {
                int pidStart = line.indexOf("pid=") + 4;
                int pidEnd = line.indexOf(",", pidStart);
                pid = line.substring(pidStart, pidEnd);
                break;
            }
        }

        // 检查退出码
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new BusinessException("命令执行失败: " + fullCommand);
        }

        // 如果未找到 PID，抛出异常或根据需求处理
        if (pid == null) {
            throw new BusinessException("未找到监听端口的 PID: " + fullCommand);
        }

        log.info("提取到的 PID: {}", pid);
        return pid; // 返回提取到的 PID
    }
}
package com.jykj.icm.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Formatter;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.io.FileUtil;

/**
 * 文件工具类
 */
public class FileUtils {
    /**
     * 计算文件MD5值
     * 
     * @param file 文件
     * @return MD5值
     */
    public static String calculateMD5(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            return calculateMD5(fis);
        } catch (IOException e) {
            throw new RuntimeException("计算MD5值失败", e);
        }
    }

    /**
     * 计算输入流的MD5值
     * 
     * @param is 输入流
     * @return MD5值
     */
    public static String calculateMD5(InputStream is) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int read;

            while ((read = is.read(buffer)) > 0) {
                digest.update(buffer, 0, read);
            }

            return bytesToHex(digest.digest());
        } catch (NoSuchAlgorithmException | IOException e) {
            throw new RuntimeException("计算MD5值失败", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        try (Formatter formatter = new Formatter()) {
            for (byte b : bytes) {
                formatter.format("%02x", b);
            }
            return formatter.toString();
        }
    }

    /**
     * 保存上传的文件
     * 
     * @param file 上传的文件
     * @param directory 保存目录
     * @return 保存的文件路径
     */
    public static String saveUploadedFile(MultipartFile file, String directory) {
        try {
            // 确保目录存在
            File dir = new File(directory);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = UUID.randomUUID().toString() + extension;

            // 保存文件
            Path targetPath = Paths.get(directory, filename);
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);

            return targetPath.toString();
        } catch (IOException e) {
            throw new RuntimeException("保存文件失败", e);
        }
    }

    /**
     * 验证文件MD5值
     * 
     * @param file 文件
     * @param expectedMd5 期望的MD5值
     * @return 验证是否通过
     */
    public static boolean verifyMD5(File file, String expectedMd5) {
        String actualMd5 = calculateMD5(file);
        return expectedMd5.equalsIgnoreCase(actualMd5);
    }

    /**
     * 验证文件MD5值通过hutools和commons-codec
     *
     * @param file 文件
     * @param expectedMd5 期望的MD5值
     * @return 验证是否通过
     */
    public static boolean verifyMD5ByHutoolsAndCodec(File file, String expectedMd5) {
        String md5 = DigestUtils.md5Hex(FileUtil.readBytes(file));
        System.out.println(md5);
        return expectedMd5.equalsIgnoreCase(md5);
    }

    public static void main(String[] args) {
        verifyMD5(new File("C:\\Users\\<USER>\\Desktop\\ytj2.0.zip"), "1");
    }

    /**
     * 解压缩文件
     * 
     * @param zipFile zip文件
     * @param targetDir 目标目录
     * @return 解压后的文件列表
     */
    public static void unzip(File zipFile, String targetDir) {
        // 确保目标目录存在
        File dir = new File(targetDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile), Charset.forName("GBK"))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File entryFile = new File(targetDir, entry.getName());

                // 创建目录
                if (entry.isDirectory()) {
                    entryFile.mkdirs();
                    continue;
                }

                // 确保父目录存在
                if (!entryFile.getParentFile().exists()) {
                    entryFile.getParentFile().mkdirs();
                }

                // 解压文件
                Files.copy(zis, entryFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
                zis.closeEntry();
            }
        } catch (IOException e) {
            throw new RuntimeException("解压文件失败", e);
        }
    }

    /**
     * 删除目录及其下所有文件
     * 
     * @param directory 目录
     */
    public static void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
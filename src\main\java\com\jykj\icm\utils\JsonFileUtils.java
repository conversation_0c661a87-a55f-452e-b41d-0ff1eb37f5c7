package com.jykj.icm.utils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * JSON文件工具类，用于读写JSON文件
 */
public class JsonFileUtils {
    private static final Logger logger = LoggerFactory.getLogger(JsonFileUtils.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将对象保存到JSON文件
     * 
     * @param <T> 对象类型
     * @param filePath 文件路径
     * @param object 要保存的对象
     * @throws IOException IO异常
     */
    public static <T> void saveObjectToJson(String filePath, T object) throws IOException {
        try {
            // 确保目录存在
            File file = new File(filePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            // 将对象写入JSON文件
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, object);
            logger.info("成功将对象保存到JSON文件: {}", filePath);
        } catch (IOException e) {
            logger.error("保存对象到JSON文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 从JSON文件加载对象
     * 
     * @param <T> 对象类型
     * @param filePath 文件路径
     * @param clazz 对象类
     * @return 加载的对象，如果文件不存在则返回null
     * @throws IOException IO异常
     */
    public static <T> T loadObjectFromJson(String filePath, Class<T> clazz) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            logger.info("JSON文件不存在: {}", filePath);
            return null;
        }

        try {
            // 从JSON文件读取对象
            T object = objectMapper.readValue(file, clazz);
            logger.info("成功从JSON文件加载对象: {}", filePath);
            return object;
        } catch (IOException e) {
            logger.error("从JSON文件加载对象失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 将对象列表保存到JSON文件
     * 
     * @param <T> 对象类型
     * @param filePath 文件路径
     * @param objects 要保存的对象列表
     * @throws IOException IO异常
     */
    public static <T> void saveListToJson(String filePath, List<T> objects) throws IOException {
        try {
            // 确保目录存在
            File file = new File(filePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            // 将对象列表写入JSON文件
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, objects);
            logger.info("成功将对象列表保存到JSON文件: {}", filePath);
        } catch (IOException e) {
            logger.error("保存对象列表到JSON文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 从JSON文件加载对象列表
     * 
     * @param <T> 对象类型
     * @param filePath 文件路径
     * @param typeReference 类型引用
     * @return 加载的对象列表，如果文件不存在则返回空列表
     * @throws IOException IO异常
     */
    public static <T> List<T> loadListFromJson(String filePath, TypeReference<List<T>> typeReference)
        throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            logger.info("JSON文件不存在: {}", filePath);
            return new ArrayList<>();
        }

        try {
            // 从JSON文件读取对象列表
            List<T> objects = objectMapper.readValue(file, typeReference);
            logger.info("成功从JSON文件加载对象列表: {}", filePath);
            return objects;
        } catch (IOException e) {
            logger.error("从JSON文件加载对象列表失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 将Map保存到JSON文件
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     * @param filePath 文件路径
     * @param map 要保存的Map
     * @throws IOException IO异常
     */
    public static <K, V> void saveMapToJson(String filePath, Map<K, V> map) throws IOException {
        try {
            // 确保目录存在
            File file = new File(filePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            // 将Map写入JSON文件
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, map);
            logger.info("成功将Map保存到JSON文件: {}", filePath);
        } catch (IOException e) {
            logger.error("保存Map到JSON文件失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 从JSON文件加载Map
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     * @param filePath 文件路径
     * @param typeReference 类型引用
     * @return 加载的Map，如果文件不存在则返回空Map
     * @throws IOException IO异常
     */
    public static <K, V> Map<K, V> loadMapFromJson(String filePath, TypeReference<Map<K, V>> typeReference)
        throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            logger.info("JSON文件不存在: {}", filePath);
            return new HashMap<>();
        }

        try {
            // 从JSON文件读取Map
            Map<K, V> map = objectMapper.readValue(file, typeReference);
            logger.info("成功从JSON文件加载Map: {}", filePath);
            return map;
        } catch (IOException e) {
            logger.error("从JSON文件加载Map失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 在Map中添加或更新键值对，并保存到JSON文件
     * 
     * @param <K> 键类型
     * @param <V> 值类型
     * @param filePath 文件路径
     * @param key 键
     * @param value 值
     * @param typeReference 类型引用
     * @throws IOException IO异常
     */
    public static <K, V> void putMapEntry(String filePath, K key, V value, TypeReference<Map<K, V>> typeReference)
        throws IOException {
        // 先加载现有Map
        Map<K, V> map = loadMapFromJson(filePath, typeReference);

        // 添加或更新键值对
        map.put(key, value);

        // 保存回文件
        saveMapToJson(filePath, map);
    }
}
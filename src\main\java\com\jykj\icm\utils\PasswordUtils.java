package com.jykj.icm.utils;

import java.util.regex.Pattern;

import cn.hutool.core.util.StrUtil;

public class PasswordUtils {

    // 密码复杂度正则：至少包含大写、小写、数字、特殊字符中的三类，长度8-32
    private static final String PASSWORD_REGEX = "^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)|" + // 大写+小写+数字
        "(?=.*[A-Z])(?=.*[a-z])(?=.*[^A-Za-z\\d])|" + // 大写+小写+特殊
        "(?=.*[A-Z])(?=.*\\d)(?=.*[^A-Za-z\\d])|" + // 大写+数字+特殊
        "(?=.*[a-z])(?=.*\\d)(?=.*[^A-Za-z\\d]))" + // 小写+数字+特殊
        "[A-Za-z\\d@$!%*?&]{8,32}$";

    /**
     * 综合密码校验方法
     * 
     * @param newPassword 新密码
     * @param username 用户名（用于检查是否包含）
     * @param currentPassword 当前密码（用于检查是否重复）
     * @return 校验结果（true=通过，false=不通过）
     */
    public static boolean validatePassword(String newPassword, String username, String currentPassword) {
        // 基础空值校验
        if (StrUtil.isBlank(newPassword)) {
            return false;
        }
        String dbNewPasswordNormal = AESUtils.isAESEnCode(newPassword) ? AESUtils.AESDeCode(newPassword) : newPassword;
        String dbOldPasswordNormal =
            AESUtils.isAESEnCode(currentPassword) ? AESUtils.AESDeCode(currentPassword) : currentPassword;

        // 1. 正则校验复杂度
        if (!Pattern.matches(PASSWORD_REGEX, dbNewPasswordNormal)) {
            return false;
        }

        // 2. 检查是否包含用户名或其倒写（不区分大小写）
        String reversedUsername = new StringBuilder(username).reverse().toString();
        String lowerPassword = dbNewPasswordNormal.toLowerCase();
        if (lowerPassword.contains(username.toLowerCase()) || lowerPassword.contains(reversedUsername.toLowerCase())) {
            return false;
        }

        // 3. 检查是否与当前密码或其倒写相同（不区分大小写）
        if (StrUtil.isBlank(dbOldPasswordNormal)) {
            String reversedCurrent = new StringBuilder(dbOldPasswordNormal).reverse().toString();
            if (dbNewPasswordNormal.equalsIgnoreCase(dbOldPasswordNormal)
                || dbNewPasswordNormal.equalsIgnoreCase(reversedCurrent)) {
                return false;
            }
        }
        return true;
    }
}
/**
 * @Title : StringUtils
 * @Package : com.scjy.client.util
 * @Description : String工具类
 * @Author: 黄杰
 * @Date: 2025-02-14 15:32
 */
package com.jykj.icm.utils;

/**
 * String工具类
 *
 * <AUTHOR>
 * @version 1.00
 * @Date 2025-02-14
 */
public class StringUtils {
    /**
     * Object转String-》获取值
     *
     * @param object Object
     * @return String类型的值
     * <AUTHOR>
     */
    public static String getValue(Object object) {
        if (object == null) {
            return "";
        }
        return String.valueOf(object);
    }
}
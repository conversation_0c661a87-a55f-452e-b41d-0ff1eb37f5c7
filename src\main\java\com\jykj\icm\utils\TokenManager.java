package com.jykj.icm.utils;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Component;

import com.jykj.icm.entity.User;

/**
 * Token管理器 负责生成、验证和删除token，token直接存储在内存中
 */
@Component
public class TokenManager {
    // 使用ConcurrentHashMap保证线程安全
    private final Map<String, TokenInfo> tokenStore = new ConcurrentHashMap<>();

    // 随机数生成器，用于生成token
    private final SecureRandom secureRandom = new SecureRandom();

    // token过期时间（分钟）
    private static final long TOKEN_EXPIRE_MINUTES = 120;

    // 定时清理过期token的线程池
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * Token信息类，包含用户信息和过期时间
     */
    private static class TokenInfo {
        private final User user;
        private final LocalDateTime expireTime;

        public TokenInfo(User user, LocalDateTime expireTime) {
            this.user = user;
            this.expireTime = expireTime;
        }

        public User getUser() {
            return user;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expireTime);
        }
    }

    public TokenManager() {
        // 启动定时任务，每10分钟清理一次过期token
        scheduler.scheduleAtFixedRate(this::cleanExpiredTokens, 10, 10, TimeUnit.MINUTES);
    }

    /**
     * 生成新token并与用户关联
     * 
     * @param user 用户信息
     * @return 生成的token
     */
    public String generateToken(User user) {
        // 生成随机token
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        String token = Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);

        // 设置过期时间
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(TOKEN_EXPIRE_MINUTES);

        // 存储token信息
        tokenStore.put(token, new TokenInfo(user, expireTime));

        return token;
    }

    /**
     * 根据token获取用户信息
     * 
     * @param token 认证token
     * @return 关联的用户信息，如果token无效或已过期则返回null
     */
    public User getUserByToken(String token) {
        TokenInfo tokenInfo = tokenStore.get(token);
        if (tokenInfo != null && !tokenInfo.isExpired()) {
            return tokenInfo.getUser();
        }
        // 如果token已过期，则移除
        if (tokenInfo != null && tokenInfo.isExpired()) {
            tokenStore.remove(token);
        }
        return null;
    }

    /**
     * 验证token是否有效
     * 
     * @param token 认证token
     * @return token是否有效
     */
    public boolean validateToken(String token) {
        TokenInfo tokenInfo = tokenStore.get(token);
        if (tokenInfo != null && !tokenInfo.isExpired()) {
            return true;
        }
        // 如果token已过期，则移除
        if (tokenInfo != null && tokenInfo.isExpired()) {
            tokenStore.remove(token);
        }
        return false;
    }

    /**
     * 删除token
     * 
     * @param token 要删除的token
     */
    public void removeToken(String token) {
        tokenStore.remove(token);
    }

    /**
     * 刷新token的过期时间
     * 
     * @param token 要刷新的token
     * @return 是否成功刷新
     */
    public boolean refreshToken(String token) {
        TokenInfo tokenInfo = tokenStore.get(token);
        if (tokenInfo != null && !tokenInfo.isExpired()) {
            // 创建新的TokenInfo，更新过期时间
            TokenInfo newTokenInfo =
                new TokenInfo(tokenInfo.getUser(), LocalDateTime.now().plusMinutes(TOKEN_EXPIRE_MINUTES));
            tokenStore.put(token, newTokenInfo);
            return true;
        }
        return false;
    }

    /**
     * 清理过期的token
     */
    private void cleanExpiredTokens() {
        tokenStore.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }
}
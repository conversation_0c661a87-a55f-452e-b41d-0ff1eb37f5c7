! Configuration File for keepalived
 
global_defs {
	 notification_email {
		 <EMAIL>
		 <EMAIL>
		 <EMAIL>
	 }
	 notification_email_from <PERSON>.<PERSON><EMAIL>
	 smtp_server ${localIp}
	 smtp_connect_timeout 30
	 router_id nginx_master
}
vrrp_script chk_http_port {
	 script "/etc/keepalived/check_nginx.sh"
	 interval 2 #（检测脚本执行的间隔）
	 weight 2
}
vrrp_instance VI_1 {
	 state ${stateType} # 备份服务器上将 MASTER 改为 BACKUP
	 interface ${networkCard} //网卡ens33
	 virtual_router_id 66 # 主、备机的 virtual_router_id 必须相同
	 priority ${priorityNum} # 主、备机取不同的优先级，主机值较大，备份机值较小
	 advert_int 1
	 authentication {
	  auth_type PASS
	  auth_pass 1111
	}
	 virtual_ipaddress {
		${vipIp}/24
	} 
	
	track_script {
	 chk_http_port
	}
}

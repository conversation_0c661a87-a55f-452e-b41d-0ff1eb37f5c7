
user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;
    client_max_body_size 100m;
    include /etc/nginx/conf.d/*.conf;

    upstream ssoserver {
      server ***************:9009; # 主机
      server ***************:9009 backup; # 备机
	  server ***************:9009 backup; # 备机
    }

   server {
	    listen 9000 ssl;  # 1.1版本后这样写
        server_name localhost; #填写绑定证书的域名
		
        ssl_certificate /etc/nginx/ssl/JYKJ.pem;  # 指定证书的位置，绝对路径
        ssl_certificate_key /etc/nginx/ssl/JYKJ.pem;  # 绝对路径，同上
		
        ssl_session_timeout 5m;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2; #按照这个协议配置
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;#按照这个套件配置
        ssl_prefer_server_ciphers on;
		
	    ssl_session_cache shared:SSL:1m;
 
        fastcgi_param  HTTPS        on;
        fastcgi_param  HTTP_SCHEME     https;
		
		
		
		location / {
			proxy_set_header   X-Real-IP         $remote_addr;
			proxy_set_header   Host              $http_host;
			proxy_set_header   X-Forwarded-For   $proxy_add_x_forwarded_for;
			proxy_pass https://ssoserver/;
		}
		
		location /sse/userConnect {
			proxy_http_version 1.1;  
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection '';
			proxy_buffering off;
			proxy_cache off;
			proxy_read_timeout 7200s;
			proxy_send_timeout 7200s;
			keepalive_timeout 7200s;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header Host $http_host;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass https://ssoserver/sse/userConnect;
		}
		
		location /loginsse/userConnect {
			proxy_http_version 1.1;  
			proxy_set_header Upgrade $http_upgrade;
			proxy_set_header Connection '';
			proxy_buffering off;
			proxy_cache off;
			proxy_read_timeout 7200s;
			proxy_send_timeout 7200s;
			keepalive_timeout 7200s;
			proxy_set_header X-Real-IP $remote_addr;
			proxy_set_header Host $http_host;
			proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
			proxy_pass https://ssoserver/loginsse/userConnect;
		}
    }
}

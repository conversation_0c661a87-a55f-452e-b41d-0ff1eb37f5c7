# 注释掉 所有IP都可以访问redis服务
#bind 127.0.0.1 -::1
# 关闭保护模式
protected-mode no
# 端口号
port 6379
# 改为no 后台进程的模式，避免跟docker的-d命令冲实
daemonize no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
# 登录密码
requirepass ${masterAuth}
# 主节点密码
masterauth ${masterAuth}
# 持久化
# 开启后，Redis会把每次写入的数据在接收后都写入 appendonly.aof 文件，每次启动时Redis都会先把这个文件的数据读入内存里
appendonly yes
#从节点需要配置这个 主节点的IP 和端口
replicaof ${masterIp} ${masterPort}

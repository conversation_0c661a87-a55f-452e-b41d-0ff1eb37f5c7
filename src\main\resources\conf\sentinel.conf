# Example sentinel.conf
# 关闭保护模式
protected-mode no
# 哨兵服务的端口号
port 26379
# 改为no 后台进程的模式，避免跟docker的-d命令冲实
daemonize no
pidfile /var/run/redis-sentinel.pid
loglevel notice
logfile ""
dir /tmp
# 添加主节点的 IP和端口 后面的这个数字1，代表主机挂了，slave投票看让谁接替成为主机，票数最多的，就会成为主机！ （我这只部署两台哨兵 所以写1）
sentinel monitor mymaster ${masterIp} ${masterPort} ${sentinelNum}
# 主节点的认证密码
sentinel auth-pass mymaster ${masterAuth}
# 监控时间 (名称) (超时时间,当超过这个时间，则认为master已经挂了)
sentinel down-after-milliseconds mymaster 50000
acllog-max-len 128
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 180000
sentinel deny-scripts-reconfig yes
SENTINEL resolve-hostnames no
SENTINEL announce-hostnames no
SENTINEL master-reboot-down-after-period mymaster 0

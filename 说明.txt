操作系统：openEuler 24.03 LTS
数据库：使用docker部署的opengauss5.0.3
部署命令：
docker network create --subnet=**********/24 opengaussnetwork
主机：docker run --network=host --privileged=true --name opengauss -h opengauss -d -e GS_PORT=5432 -e OG_SUBNET=opengaussnetwork -e GS_PASSWORD=Jykj1994@ -e NODE_NAME=opengauss -e REPL_CONN_INFO="replconninfo1 = 'localhost=*************** localport=5434 localservice=5432 remotehost=*************** remoteport=5434 remoteservice=5432'\n" -v /home/<USER>/gauss5:/var/lib/opengauss enmotech/opengauss:5.0.3 -M primary

docker network create --subnet=**********/24 opengaussnetwork
备机：docker run --network=host --privileged=true --name opengauss -h opengauss -d -e GS_PORT=5432 -e OG_SUBNET=opengaussnetwork -e GS_PASSWORD=Jykj1994@ -e NODE_NAME=opengauss -e REPL_CONN_INFO="replconninfo1 = 'localhost=*************** localport=5434 localservice=5432 remotehost=*************** remoteport=5434 remoteservice=5432'\n" -v /home/<USER>/gauss5:/var/lib/opengauss enmotech/opengauss:5.0.3 -M standby



特殊处理replconninfo：
************* 主
replconninfo1='localhost=************* localport=26001 localheartbeatport=26005 localservice=26004 remotehost=************* remoteport=26001 remoteheartbeatport=26005 remoteservice=26004'
replconninfo2='localhost=************* localport=26001 localheartbeatport=26005 localservice=26004 remotehost=************ remoteport=26001 remoteheartbeatport=26005 remoteservice=26004'


************* 备
replconninfo1='localhost=************* localport=26001 localheartbeatport=26005 localservice=26004 remotehost=************* remoteport=26001 remoteheartbeatport=26005 remoteservice=26004'
replconninfo2='localhost=************* localport=26001 localheartbeatport=26005 localservice=26004 remotehost=************ remoteport=26001 remoteheartbeatport=26005 remoteservice=26004'



************ 备
replconninfo1='localhost=************ localport=26001 localheartbeatport=26005 localservice=26004 remotehost=************* remoteport=26001 remoteheartbeatport=26005 remoteservice=26004'
replconninfo2='localhost=************ localport=26001 localheartbeatport=26005 localservice=26004 remotehost=************* remoteport=26001 remoteheartbeatport=26005 remoteservice=26004'
